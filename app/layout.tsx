import './globals.css';
import type { Metadata } from 'next';
import { ThemeProvider } from '@/components/theme-provider';

export const metadata: Metadata = {
  title: 'LinkSnap - Effortless LinkedIn Data Extraction',
  description: 'Professional LinkedIn scraper for extracting profiles, companies, jobs, and posts data',
  keywords: 'linkedin, scraper, data extraction, profiles, companies, jobs',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta name="theme-color" content="#FFFFFF" />
      </head>
      <body>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}